<?php

use App\Http\Controllers\SupperAdmin\CategoryController;
use App\Http\Controllers\SupperAdmin\FaqController;
use App\Http\Controllers\SupperAdmin\NoteController;
use App\Http\Controllers\SupperAdmin\NotificationController;
use App\Http\Controllers\SupperAdmin\OrderController;
use App\Http\Controllers\SupperAdmin\PackageController;
use App\Http\Controllers\SupperAdmin\PackageListController;
use App\Http\Controllers\SupperAdmin\PaymentController;
use App\Http\Controllers\SupperAdmin\PlatformController;
use App\Http\Controllers\SupperAdmin\PostController;
use App\Http\Controllers\SupperAdmin\ProviderController;
use App\Http\Controllers\SupperAdmin\TenantController;
use App\Http\Controllers\SupperAdmin\TenantSettingController;
use App\Http\Controllers\SupperAdmin\TransactionController;
use App\Http\Controllers\SupperAdmin\VoucherController;
use Illuminate\Support\Facades\Route;

Route::prefix('supper-admin')
    ->as('supper-admin.')
    ->middleware(['access.tenant','identify.tenant','supper-admin'])
    ->group(function () {
        Route::resource('platforms', PlatformController::class)
            ->names('platforms')
            ->parameters(['platforms' => 'platform']);
        Route::get('platforms/ajax/data', [PlatformController::class, 'ajaxData'])
            ->name('platforms.ajax.data');
        Route::post('platforms/bulk-action', [PlatformController::class, 'bulkAction'])
            ->name('platforms.bulk-action');

        Route::resource('categories', CategoryController::class)
            ->names('categories')
            ->parameters(['categories' => 'category']);
        Route::get('categories/ajax/data', [CategoryController::class, 'ajaxData'])
            ->name('categories.ajax.data');
        Route::post('categories/bulk-action', [CategoryController::class, 'bulkAction'])
            ->name('categories.bulk-action');

        Route::resource('providers', ProviderController::class)->names('providers')
            ->names('providers')
            ->parameters(['providers' => 'provider']);
        Route::get('providers/ajax/data', [ProviderController::class, 'ajaxData'])
            ->name('providers.ajax.data');
        Route::post('providers/bulk-action', [ProviderController::class, 'bulkAction'])
            ->name('providers.bulk-action');

        Route::resource('packages', PackageController::class)
            ->names('packages')
            ->parameters(['packages' => 'package']);
        Route::get('packages/ajax/data', [PackageController::class, 'ajaxData'])
            ->name('packages.ajax.data');
        Route::post('packages/bulk-action', [PackageController::class, 'bulkAction'])
            ->name('packages.bulk-action');

        Route::resource('package-lists', PackageListController::class)
            ->names('package-lists')
            ->parameters(['package-lists' => 'package-list']);
        Route::get('package-lists/ajax/data', [PackageListController::class, 'ajaxData'])
            ->name('package-lists.ajax.data');
        Route::post('package-lists/bulk-action', [PackageListController::class, 'bulkAction'])
            ->name('package-lists.bulk-action');

        Route::resource('tenants', TenantController::class)->names('tenants');
        Route::get('tenants/{tenant}/settings', [TenantSettingController::class, 'edit'])
            ->name('tenants.settings.edit');

        Route::put('tenants/{tenant}/settings', [TenantSettingController::class, 'update'])
            ->name('tenants.settings.update');
        Route::get('/provider/get-services', [ProviderController::class, 'getAllServices'])
            ->name('providers.get-services');

        Route::get('/transactions', [TransactionController::class, 'index'])->name('transactions.index');
        Route::get('/transactions/ajax/data',[TransactionController::class,'ajaxData'])->name('transactions.ajax.data');
        Route::resource('faqs', FaqController::class)->names('faqs');

        Route::resource('notes', NoteController::class);
        Route::resource('posts', PostController::class)->names('posts');

        Route::resource('notifications', NotificationController::class);
        Route::resource('payments', PaymentController::class)->only(['index', 'show']);
        Route::resource('orders', OrderController::class)->only(['index', 'edit']);

        Route::resource('vouchers', VoucherController::class)
            ->names('vouchers')
            ->parameters(['vouchers' => 'voucher']);
        Route::get('vouchers/ajax/data', [VoucherController::class, 'ajaxData'])
            ->name('vouchers.ajax.data');
        Route::post('vouchers/bulk-action', [VoucherController::class, 'bulkAction'])
            ->name('vouchers.bulk-action');
    });
