<?php

use App\Http\Controllers\Client\DepositController;
use App\Http\Controllers\Client\ManageOrderController;
use App\Http\Controllers\Client\OrderController;
use App\Http\Controllers\Client\OrderHistoryController;
use App\Http\Controllers\Client\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Client\ReportController;
use App\Http\Controllers\Client\ServiceController;

require_once __DIR__. '/admin.php';
require_once __DIR__ . '/supper-admin.php';
Route::get('/', function () {
    return view('welcome');
});

Auth::routes();
Route::middleware(['access.tenant','identify.tenant'])->as('client.')->group(function () {
    Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
    Route::get('/orders/report', [ManageOrderController::class, 'index'])->name('orders.report');
    Route::get('/orders/report/ajax', [ManageOrderController::class, 'ajax'])->name('orders.report.ajax');
    Route::get('/orders/history/ajax', [OrderHistoryController::class, 'ajax'])->name('orders.history.ajax');
    Route::get('/orders/history/counts', [OrderHistoryController::class, 'counts'])->name('orders.history.counts');
    Route::get('{platform}/{category}', [ServiceController::class, 'index'])->name('service.index');
    Route::get('/load/services/{platform}/{category}', [ServiceController::class, 'loadService'])->name('service.load');

    Route::post('/order/services',[OrderController::class, 'order'])->name('order.service');

    Route::get('/report',[ReportController::class, 'index'])->name('report.index');
    Route::get('/ajax/reports/log', [ReportController::class, 'filter'])->name('report.filter');
    Route::get('deposit',[DepositController::class, 'index'])->name('deposit.index');

    Route::post('/news/update/payment',[DepositController::class, 'checkUpdate'])->name('news.update.payment');

    Route::get('profile', [ProfileController::class, 'index'])->name('profile.index');
    Route::post('profile/update', [ProfileController::class, 'update'])->name('profile.update');
});


