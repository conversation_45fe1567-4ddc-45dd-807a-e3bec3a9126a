<?php

namespace App\Services;

use App\Models\User;
use App\Models\Voucher;
use App\Models\UserVoucher;
use App\Enums\VoucherTypeEnum;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class VoucherService
{
    /**
     * Tìm voucher có thể áp dụng cho user trong tenant
     */
    public function findApplicableVoucher($userId, $tenantId): ?Voucher
    {
        return Voucher::query()
            ->where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->whereDoesntHave('userVouchers', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->first();
    }

    /**
     * Tính toán bonus amount từ voucher
     */
    public function calculateBonus(Voucher $voucher, $originalAmount): float
    {
        if ($voucher->type === VoucherTypeEnum::PERCENT) {
            return $originalAmount * ($voucher->value / 100);
        } elseif ($voucher->type === VoucherTypeEnum::FIXED) {
            return $voucher->value;
        }

        return 0;
    }

    /**
     * Áp dụng voucher cho user
     */
    public function applyVoucher(User $user, Voucher $voucher, $originalAmount, $tenantId): array
    {
        // Kiểm tra user đã sử dụng voucher này chưa
        if ($user->hasUsedVoucher($voucher->id)) {
            throw new \Exception('Bạn đã sử dụng voucher này rồi.');
        }

        // Kiểm tra voucher còn hiệu lực không
        if (!$voucher->canBeUsedByUser($user->id)) {
            throw new \Exception('Voucher không còn hiệu lực hoặc đã được sử dụng.');
        }

        $bonusAmount = $this->calculateBonus($voucher, $originalAmount);
        $totalAmount = $originalAmount + $bonusAmount;

        // Lưu record sử dụng voucher
        UserVoucher::create([
            'user_id' => $user->id,
            'voucher_id' => $voucher->id,
            'tenant_id' => $tenantId,
            'amount_bonus' => $bonusAmount,
            'original_amount' => $originalAmount,
            'used_at' => now()
        ]);

        return [
            'original_amount' => $originalAmount,
            'bonus_amount' => $bonusAmount,
            'total_amount' => $totalAmount,
            'voucher_code' => $voucher->code,
            'voucher_type' => $voucher->type->value,
            'voucher_value' => $voucher->value
        ];
    }

    /**
     * Xử lý deposit với voucher (nếu có)
     */
    public function processDepositWithVoucher(User $user, $amount, $tenantId): array
    {
        $result = [
            'original_amount' => $amount,
            'bonus_amount' => 0,
            'total_amount' => $amount,
            'voucher_applied' => false,
            'voucher_info' => null
        ];

        // Tìm voucher có thể áp dụng
        $voucher = $this->findApplicableVoucher($user->id, $tenantId);

        if ($voucher) {
            try {
                $voucherResult = $this->applyVoucher($user, $voucher, $amount, $tenantId);
                
                $result['bonus_amount'] = $voucherResult['bonus_amount'];
                $result['total_amount'] = $voucherResult['total_amount'];
                $result['voucher_applied'] = true;
                $result['voucher_info'] = [
                    'code' => $voucherResult['voucher_code'],
                    'type' => $voucherResult['voucher_type'],
                    'value' => $voucherResult['voucher_value']
                ];
            } catch (\Exception $e) {
                // Log error nhưng không throw exception để không ảnh hưởng đến deposit
                \Log::warning('Voucher application failed: ' . $e->getMessage(), [
                    'user_id' => $user->id,
                    'voucher_id' => $voucher->id,
                    'amount' => $amount
                ]);
            }
        }

        return $result;
    }

    /**
     * Kiểm tra voucher có thể sử dụng không
     */
    public function canUseVoucher(User $user, Voucher $voucher): bool
    {
        return $voucher->canBeUsedByUser($user->id);
    }

    /**
     * Lấy danh sách voucher available cho user trong tenant
     */
    public function getAvailableVouchersForUser($userId, $tenantId)
    {
        return Voucher::query()
            ->where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->whereDoesntHave('userVouchers', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->get();
    }

    /**
     * Lấy thống kê sử dụng voucher
     */
    public function getVoucherUsageStats($voucherId): array
    {
        $voucher = Voucher::findOrFail($voucherId);
        $usages = $voucher->userVouchers()->with('user')->get();

        return [
            'voucher' => $voucher,
            'total_usage' => $usages->count(),
            'total_bonus_given' => $usages->sum('amount_bonus'),
            'total_original_amount' => $usages->sum('original_amount'),
            'usage_details' => $usages
        ];
    }
}
