<?php

namespace App\Http\Traits;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\Model;

trait BulkActionTrait
{
    /**
     * Handle bulk actions for the model
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|integer',
            'action' => 'required|string|in:delete,activate,deactivate,export,duplicate,force-delete'
        ]);

        $ids = $request->input('ids');
        $action = $request->input('action');
        $modelClass = $this->getModelClass();

        try {
            $result = $this->executeBulkAction($modelClass, $ids, $action);
            
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'affected_count' => $result['count']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Execute the bulk action
     *
     * @param string $modelClass
     * @param array $ids
     * @param string $action
     * @return array
     */
    protected function executeBulkAction(string $modelClass, array $ids, string $action): array
    {
        switch ($action) {
            case 'delete':
                return $this->bulkDelete($modelClass, $ids);
            case 'activate':
                return $this->bulkActivate($modelClass, $ids);
            case 'deactivate':
                return $this->bulkDeactivate($modelClass, $ids);
            case 'export':
                return $this->bulkExport($modelClass, $ids);
            case 'duplicate':
                return $this->bulkDuplicate($modelClass, $ids);
            case 'force-delete':
                return $this->bulkForceDelete($modelClass, $ids);
            default:
                throw new \InvalidArgumentException('Hành động không được hỗ trợ: ' . $action);
        }
    }

    /**
     * Bulk delete records
     *
     * @param string $modelClass
     * @param array $ids
     * @return array
     */
    protected function bulkDelete(string $modelClass, array $ids): array
    {
        $count = $modelClass::whereIn('id', $ids)->delete();
        
        return [
            'count' => $count,
            'message' => "Đã xóa thành công {$count} mục."
        ];
    }

    /**
     * Bulk activate records
     *
     * @param string $modelClass
     * @param array $ids
     * @return array
     */
    protected function bulkActivate(string $modelClass, array $ids): array
    {
        $statusField = $this->getStatusField();
        $activeValue = $this->getActiveValue();
        
        $count = $modelClass::whereIn('id', $ids)->update([$statusField => $activeValue]);
        
        return [
            'count' => $count,
            'message' => "Đã kích hoạt thành công {$count} mục."
        ];
    }

    /**
     * Bulk deactivate records
     *
     * @param string $modelClass
     * @param array $ids
     * @return array
     */
    protected function bulkDeactivate(string $modelClass, array $ids): array
    {
        $statusField = $this->getStatusField();
        $inactiveValue = $this->getInactiveValue();
        
        $count = $modelClass::whereIn('id', $ids)->update([$statusField => $inactiveValue]);
        
        return [
            'count' => $count,
            'message' => "Đã vô hiệu hóa thành công {$count} mục."
        ];
    }

    /**
     * Bulk export records
     *
     * @param string $modelClass
     * @param array $ids
     * @return array
     */
    protected function bulkExport(string $modelClass, array $ids): array
    {
        // This is a placeholder - implement actual export logic
        $count = count($ids);
        
        return [
            'count' => $count,
            'message' => "Đã chuẩn bị xuất {$count} mục. Tính năng này sẽ được triển khai sau."
        ];
    }

    /**
     * Bulk duplicate records
     *
     * @param string $modelClass
     * @param array $ids
     * @return array
     */
    protected function bulkDuplicate(string $modelClass, array $ids): array
    {
        $records = $modelClass::whereIn('id', $ids)->get();
        $count = 0;
        
        foreach ($records as $record) {
            $newRecord = $record->replicate();
            if (method_exists($newRecord, 'name')) {
                $newRecord->name = $newRecord->name . ' (Copy)';
            }
            $newRecord->save();
            $count++;
        }
        
        return [
            'count' => $count,
            'message' => "Đã nhân bản thành công {$count} mục."
        ];
    }

    /**
     * Bulk force delete records
     *
     * @param string $modelClass
     * @param array $ids
     * @return array
     */
    protected function bulkForceDelete(string $modelClass, array $ids): array
    {
        $count = $modelClass::whereIn('id', $ids)->forceDelete();
        
        return [
            'count' => $count,
            'message' => "Đã xóa vĩnh viễn thành công {$count} mục."
        ];
    }

    /**
     * Get the model class for bulk actions
     * Override this method in your controller
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        throw new \BadMethodCallException('Method getModelClass() must be implemented in the controller.');
    }

    /**
     * Get the status field name
     * Override this method if your model uses a different status field
     *
     * @return string
     */
    protected function getStatusField(): string
    {
        return 'status';
    }

    /**
     * Get the active value for status field
     * Override this method if your model uses a different active value
     *
     * @return mixed
     */
    protected function getActiveValue()
    {
        return 'active';
    }

    /**
     * Get the inactive value for status field
     * Override this method if your model uses a different inactive value
     *
     * @return mixed
     */
    protected function getInactiveValue()
    {
        return 'inactive';
    }
}
