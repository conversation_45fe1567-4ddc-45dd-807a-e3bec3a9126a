<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use App\Models\Tenant;
use App\Enums\BaseStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class FaqController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $faqs = Faq::with('tenant')
                ->select('id', 'question', 'answer', 'created_at', 'status', 'tenant_id');

            if ($request->has('order_by') && $request->has('order_dir')) {
                $allowedColumns = ['id', 'question', 'answer', 'created_at', 'status', 'tenant_id'];
                $orderBy = $request->get('order_by');
                $orderDir = $request->get('order_dir');
                if (in_array($orderBy, $allowedColumns)) {
                    $faqs->orderBy($orderBy, $orderDir);
                }
            }

            return DataTables::of($faqs)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.faqs.edit', $row->id);
                    $deleteUrl = route('supper-admin.faqs.destroy', $row->id);

                    return '
                        <a href="' . $editUrl . '" class="btn-action text-primary me-1" title="Sửa">
                            <i class="ri-edit-2-line"></i>
                        </a>
                        <button type="button" class="btn-action text-danger btn-delete"
                            data-id="' . $row->id . '"
                            data-url="' . $deleteUrl . '"
                            title="Xóa">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    ';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->editColumn('status', function ($row) {
                    return $row->status->toHtml();
                })
                ->editColumn('tenant', function ($row) {
                    return '<span class="badge bg-purple">'.$row->tenant->domain.'</span>' ?? 'N/A';
                })
                ->rawColumns(['action', 'status', 'tenant'])
                ->make(true);
        }

        return view('supper-admin.faqs.index');
    }

    public function create()
    {
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();
        return view('supper-admin.faqs.create', compact('tenants'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'question' => 'required|string|max:255',
            'answer' => 'required|string',
            'status' => 'nullable|string|max:60|in:published,draft,pending',
        ]);

        DB::beginTransaction();
        try {
            Faq::create($validated);
            DB::commit();

            return $this->httpResponse()->setNextUrl(route('supper-admin.faqs.index'))->withCreatedSuccessMessage();

        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi thêm câu hỏi: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi thêm mới câu hỏi. Vui lòng thử lại!']);
        }
    }

    public function edit(string|int $id)
    {
        $faq = Faq::query()->findOrFail($id);
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.faqs.edit', [
            'dataEdit' => $faq,
            'tenants' => $tenants,
        ]);
    }

    public function update(Request $request, string|int $id)
    {
        $faq = Faq::query()->findOrFail($id);

        $validated = $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'question' => 'required|string|max:255',
            'answer' => 'required|string',
            'status' => 'nullable|string|max:60|in:published,draft,pending',
        ]);

        DB::beginTransaction();
        try {
            $faq->update($validated);
            DB::commit();

            return $this->httpResponse()->setNextUrl(route('supper-admin.faqs.index'))->withUpdatedSuccessMessage();
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi cập nhật câu hỏi: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi cập nhật câu hỏi. Vui lòng thử lại!']);
        }
    }

    public function destroy(string|int $id)
    {
        $faq = Faq::query()->findOrFail($id);

        DB::beginTransaction();
        try {
            $faq->delete();
            DB::commit();

            return response()->json([
                'error' => false,
                'message' => 'Xóa câu hỏi thành công',
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi xóa câu hỏi: ' . $exception->getMessage());

            return response()->json([
                'error' => true,
                'message' => 'Lỗi hệ thống khi xóa câu hỏi. Vui lòng thử lại!',
            ]);
        }
    }
}
