<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\PackageList;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PackageListController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/supper-admin/datatable-package-list.min.js']);
        return view('supper-admin.package-lists.index');
    }
    public function ajaxData()
    {
        $data = PackageList::query()->select('id', 'name', 'key', 'created_at', 'status');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'name', 'key', 'created_at', 'status'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%')
                        ->orWhere('key', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);
    }
    public function create()
    {
        return view('supper-admin.package-lists.create');
    }

    public function store(Request $request)
    {
        $payload = $request->validate([
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:package_lists,key',
            'status' => 'required',
        ]);

        PackageList::query()->create($payload);

        return $this->httpResponse()->setNextUrl(route('supper-admin.package-lists.index'))->withCreatedSuccessMessage();
    }

    public function edit(string|int $id)
    {
        $packageList = PackageList::query()->findOrFail($id);
        return view('supper-admin.package-lists.edit', [
            'dataEdit' => $packageList,
        ]);
    }

    public function update(string|int $id, Request $request)
    {
        $packageList = PackageList::query()->findOrFail($id);

        $payload = $request->validate([
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:package_lists,key,' . $id,
            'status' => 'required',
        ]);

        $packageList->update($payload);

        return $this->httpResponse()->setNextUrl(route('supper-admin.package-lists.index'))->withUpdatedSuccessMessage();
    }
    public function destroy(string|int $id)
    {
        $packageList = PackageList::query()->findOrFail($id);
        $packageList->delete();

        return $this->httpResponse()->setNextUrl(route('supper-admin.package-lists.index'))->withDeletedSuccessMessage();
    }
}
