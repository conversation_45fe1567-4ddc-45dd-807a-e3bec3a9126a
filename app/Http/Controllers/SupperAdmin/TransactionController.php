<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class TransactionController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/supper-admin/datatable-transaction.min.js']);
        return view('supper-admin.transactions.index');
    }

    public function ajaxData()
    {
        $data = Transaction::query()->select('id', 'code','user_id','tenant_id','balance_before', 'balance_after', 'amount', 'created_at', 'status', 'description', 'type','math');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'code', 'user_id', 'tenant_id', 'balance_before', 'balance_after', 'amount', 'created_at', 'status', 'type', 'math'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->addColumn('username', function ($row) {
                return $row->user->username;
            })
            ->addColumn('tenant', function ($row) {
                return $row->tenant->domain;
            })
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('code', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);
    }
}
