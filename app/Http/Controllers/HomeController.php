<?php

namespace App\Http\Controllers;

use App\Enums\BaseStatusEnum;
use App\Enums\PostTypeEnum;
use App\Models\Faq;
use App\Models\Notification;
use App\Models\Post;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index(Request $request)
    {
        $tenantId = $request->tenant->id;
        $faqs = Faq::query()
            ->where('tenant_id', $tenantId)
            ->get();

        $notifications = Notification::query()
            ->where('tenant_id', $tenantId)
            ->where('user_id', auth()->id())
            ->orderBy('id', 'desc')
            ->get();
        // Lấy posts cho danh sách bài viết (type = 'post' hoặc 'both')
        $posts = Post::query()
            ->where('tenant_id', $tenantId)
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->whereIn('type', [PostTypeEnum::POST->value, PostTypeEnum::BOTH->value])
            ->orderByDesc('pin')
            ->orderByDesc('created_at')
            ->get();

        // Lấy posts cho popup (type = 'popup' hoặc 'both')
        $popupPosts = Post::query()
            ->where('tenant_id', $tenantId)
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->whereIn('type', [PostTypeEnum::POPUP->value, PostTypeEnum::BOTH->value])
            ->orderByDesc('pin')
            ->orderByDesc('created_at')
            ->get();

        return view('clients.dashboard.index', [
            'faqs' => $faqs,
            'notifications' => $notifications,
            'posts' => $posts,
            'popupPosts' => $popupPosts,
        ]);
    }
}
