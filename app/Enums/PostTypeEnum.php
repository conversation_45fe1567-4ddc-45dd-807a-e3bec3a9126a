<?php

namespace App\Enums;

enum PostTypeEnum: string
{
    case POST = 'post';

    case POPUP = 'popup';

    case BOTH = 'both';

    public function label(): string
    {
        return match ($this) {
            self::POST => 'Bài viết',
            self::POPUP => 'Popup',
            self::BOTH => 'Bài viết + Popup',
        };
    }

    public function toHtml(): string
    {
        return match ($this) {
            self::POST => '<span class="badge bg-primary">Bài viết</span>',
            self::POPUP => '<span class="badge bg-pink">Popup</span>',
            self::BOTH => '<span class="badge bg-success">Bài viết + Popup</span>',
        };
    }
}
