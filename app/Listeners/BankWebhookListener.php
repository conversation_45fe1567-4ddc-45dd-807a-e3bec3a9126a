<?php

namespace App\Listeners;

use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Enums\TypeNotificationEnum;
use App\Events\BankWebhookEvent;
use App\Models\Notification;
use App\Models\Transaction;
use App\Models\User;
use App\Services\VoucherService;
use Illuminate\Support\Facades\DB;

class BankWebhookListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BankWebhookEvent $event): void
    {
        if (strtolower($event->bankWebhookData->transferType) !== 'in') {
            return;
        }

        $data = $event->bankWebhookData;
        $user = User::query()->where('code', $event->info)->first();

        if (!$user) {
            return;
        }

        DB::transaction(function () use ($user, $data) {
            $amount = $data->transferAmount;
            $tenantId = optional($user->tenants()->first())->id ?? config('app.default_tenant_id');

            $voucherService = new VoucherService();
            $voucherResult = $voucherService->processDepositWithVoucher($user, $amount, $tenantId);

            $totalAmount = $voucherResult['total_amount'];
            $bonusAmount = $voucherResult['bonus_amount'];
            $balanceBefore = $user->balance;
            $balanceAfter = $balanceBefore + $totalAmount;

            $user->update([
                'balance' => $balanceAfter,
                'total_deposit' => $user->total_deposit + $totalAmount,
            ]);


            Transaction::query()->create([
                'user_id' => $user->id,
                'tenant_id' => $tenantId,
                'admin_id' => $user->id,
                'type' => TransactionTypeEnum::DEPOSIT->value,
                'math' => '+',
                'amount' => $totalAmount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'status' => TransactionStatusEnum::COMPLETED->value,
                'description' => 'Nạp tiền qua ' . $data->gateway,
            ]);

            $notificationContent = 'Nạp thành công <span class="text-orange fw-semibold">'.number_format($amount).'</span>';
            if ($voucherResult['voucher_applied']) {
                $notificationContent .= ' + Cộng thêm <span class="text-success fw-semibold">'.number_format($bonusAmount).'</span> từ khuyến mãi';
            }

            Notification::query()->create([
                'title' => 'Nạp tiền thành công',
                'content' => $notificationContent,
                'tenant_id' => $tenantId,
                'user_id' => $user->id,
                'is_read' => false,
                'amount' => $totalAmount,
                'type' => TypeNotificationEnum::DEPOSIT->value,
            ]);
        });
    }

}
