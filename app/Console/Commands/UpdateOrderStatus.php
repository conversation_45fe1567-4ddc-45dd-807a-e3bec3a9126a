<?php

namespace App\Console\Commands;

use App\Enums\OrderStatusEnum;
use App\Enums\SourceStatusEnum;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class UpdateOrderStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cập nhật trạng thái đơn hàng tự động';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $orders = Order::query()->whereNotNull('source_id')
            ->whereIn('source_status', [
                SourceStatusEnum::PROCESSING->value,
                SourceStatusEnum::PENDING->value,
            ])->get();

        foreach ($orders as $order) {
            $postData = [
                'key' => $order->provider->key,
                'action' => 'status',
                'order' => $order->source_id,
            ];

            try {
                $response = Http::timeout(60)->asForm()->post($order->provider->url, $postData)->json();

                if (!$response || !isset($response['status'])) {
                    Log::error("Không lấy được trạng thái đơn hàng #{$order->id}", [
                        'provider_url' => $order->provider->url,
                        'post_data' => $postData,
                        'response' => $response,
                    ]);
                    $this->error("Không thể lấy trạng thái đơn hàng #{$order->id}");
                    continue;
                }

                $status = $response['status'];
                $sourceStatus = $this->mapStatus($status);
                $orderStatus = $this->mapStatus($status, true);

                $order->update([
                    'source_resp' => $response,
                    'source_status' => $sourceStatus,
                    'order_status' => $orderStatus,
                ]);

                $this->info("Đã cập nhật đơn hàng #{$order->id} => $status");
            } catch (\Throwable $e) {
                Log::error("Lỗi khi cập nhật đơn hàng #{$order->id}: " . $e->getMessage(), [
                    'exception' => $e,
                    'provider_url' => $order->provider->url,
                    'post_data' => $postData,
                ]);
                $this->error("Lỗi đơn hàng #{$order->id}: " . $e->getMessage());
            }
        }
    }
    protected function mapStatus($status, $isOrder = false)
    {
        $map = [
            'Processing' => SourceStatusEnum::PROCESSING->value,
            'In progress' => SourceStatusEnum::PROCESSING->value,
            'Completed' => SourceStatusEnum::COMPLETED->value,
            'Canceled' => SourceStatusEnum::CANCELLED->value,
            'Refunded' => SourceStatusEnum::REFUNDED->value,
            'Waiting Cancel' => SourceStatusEnum::WAITING_CANCEL->value,
            'Pending' => SourceStatusEnum::PENDING->value,
        ];

        if ($isOrder) {
            $map = [
                'Processing' => OrderStatusEnum::PROCESSING->value,
                'In progress' => OrderStatusEnum::PROCESSING->value,
                'Completed' => OrderStatusEnum::COMPLETED->value,
                'Canceled' => OrderStatusEnum::CANCELLED->value,
                'Refunded' => OrderStatusEnum::REFUNDED->value,
                'Waiting Cancel' => OrderStatusEnum::WAITING_CANCEL->value,
                'Pending' => OrderStatusEnum::PENDING->value,
            ];
        }

        return $map[$status] ?? SourceStatusEnum::PENDING->value;
    }
}
