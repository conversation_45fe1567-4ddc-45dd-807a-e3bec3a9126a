<?php

namespace Tests\Feature;

use App\Enums\BaseStatusEnum;
use App\Enums\PostTypeEnum;
use App\Models\Post;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NotificationPostTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $tenant;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo user và tenant để test
        $this->user = User::factory()->create();
        $this->tenant = Tenant::factory()->create(['owner_id' => $this->user->id]);
    }

    /** @test */
    public function admin_can_create_post_with_article_type()
    {
        $this->actingAs($this->user);

        $postData = [
            'title' => 'Test Article Post',
            'content' => 'This is a test article content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::POST->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
            'pin' => false,
        ];

        $response = $this->post(route('supper-admin.posts.store'), $postData);

        $response->assertRedirect(route('supper-admin.posts.index'));
        $this->assertDatabaseHas('posts', [
            'title' => 'Test Article Post',
            'type' => PostTypeEnum::POST->value,
            'tenant_id' => $this->tenant->id,
        ]);
    }

    /** @test */
    public function admin_can_create_post_with_popup_type()
    {
        $this->actingAs($this->user);

        $postData = [
            'title' => 'Test Popup Post',
            'content' => 'This is a test popup content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::POPUP->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
            'pin' => false,
        ];

        $response = $this->post(route('supper-admin.posts.store'), $postData);

        $response->assertRedirect(route('supper-admin.posts.index'));
        $this->assertDatabaseHas('posts', [
            'title' => 'Test Popup Post',
            'type' => PostTypeEnum::POPUP->value,
            'tenant_id' => $this->tenant->id,
        ]);
    }

    /** @test */
    public function admin_can_create_post_with_both_type()
    {
        $this->actingAs($this->user);

        $postData = [
            'title' => 'Test Both Post',
            'content' => 'This is a test both content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::BOTH->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
            'pin' => false,
        ];

        $response = $this->post(route('supper-admin.posts.store'), $postData);

        $response->assertRedirect(route('supper-admin.posts.index'));
        $this->assertDatabaseHas('posts', [
            'title' => 'Test Both Post',
            'type' => PostTypeEnum::BOTH->value,
            'tenant_id' => $this->tenant->id,
        ]);
    }

    /** @test */
    public function dashboard_shows_correct_posts_for_article_list()
    {
        $this->actingAs($this->user);

        // Tạo các posts với type khác nhau
        $articlePost = Post::create([
            'title' => 'Article Post',
            'content' => 'Article content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::POST->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
        ]);

        $popupPost = Post::create([
            'title' => 'Popup Post',
            'content' => 'Popup content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::POPUP->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
        ]);

        $bothPost = Post::create([
            'title' => 'Both Post',
            'content' => 'Both content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::BOTH->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
        ]);

        $response = $this->get(route('client.home'));

        $response->assertStatus(200);
        
        // Kiểm tra posts hiển thị trong danh sách (type = 'post' hoặc 'both')
        $response->assertSee('Article Post');
        $response->assertSee('Both Post');
        $response->assertDontSee('Popup Post'); // Popup post không hiển thị trong danh sách
    }

    /** @test */
    public function dashboard_provides_correct_popup_posts()
    {
        $this->actingAs($this->user);

        // Tạo các posts với type khác nhau
        Post::create([
            'title' => 'Article Post',
            'content' => 'Article content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::POST->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
        ]);

        Post::create([
            'title' => 'Popup Post',
            'content' => 'Popup content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::POPUP->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
        ]);

        Post::create([
            'title' => 'Both Post',
            'content' => 'Both content',
            'tenant_id' => $this->tenant->id,
            'type' => PostTypeEnum::BOTH->value,
            'status' => BaseStatusEnum::PUBLISHED->value,
        ]);

        $response = $this->get(route('client.home'));

        $response->assertStatus(200);
        
        // Kiểm tra popupPosts được truyền vào view
        $response->assertViewHas('popupPosts');
        $popupPosts = $response->viewData('popupPosts');
        
        // Chỉ có 2 posts: popup và both
        $this->assertCount(2, $popupPosts);
        $this->assertTrue($popupPosts->contains('title', 'Popup Post'));
        $this->assertTrue($popupPosts->contains('title', 'Both Post'));
        $this->assertFalse($popupPosts->contains('title', 'Article Post'));
    }

    /** @test */
    public function post_type_enum_returns_correct_labels()
    {
        $this->assertEquals('Bài viết', PostTypeEnum::POST->label());
        $this->assertEquals('Popup', PostTypeEnum::POPUP->label());
        $this->assertEquals('Bài viết + Popup', PostTypeEnum::BOTH->label());
    }

    /** @test */
    public function post_type_enum_returns_correct_html()
    {
        $this->assertStringContainsString('Bài viết', PostTypeEnum::POST->toHtml());
        $this->assertStringContainsString('Popup', PostTypeEnum::POPUP->toHtml());
        $this->assertStringContainsString('Bài viết + Popup', PostTypeEnum::BOTH->toHtml());
    }
}
