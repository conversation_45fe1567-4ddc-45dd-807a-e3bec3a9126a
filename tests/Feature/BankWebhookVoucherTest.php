<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Tenant;
use App\Models\Voucher;
use App\Models\TransactionBank;
use App\Events\BankWebhookEvent;
use App\Datas\BankWebhookData;
use App\Enums\VoucherTypeEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BankWebhookVoucherTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $tenant;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo user owner
        $owner = User::factory()->create();
        
        // Tạo tenant
        $this->tenant = Tenant::create([
            'domain' => 'test.example.com',
            'verified' => true,
            'status' => 'published',
            'owner_id' => $owner->id,
            'is_primary' => true
        ]);
        
        // Tạo user
        $this->user = User::factory()->create([
            'balance' => 0,
            'total_deposit' => 0
        ]);
        $this->user->tenants()->attach($this->tenant->id, ['role' => 'user']);
    }

    public function test_bank_webhook_applies_voucher_first_time()
    {
        // Tạo voucher 10%
        $voucher = Voucher::create([
            'tenant_id' => $this->tenant->id,
            'code' => 'WELCOME10',
            'type' => VoucherTypeEnum::PERCENT,
            'value' => 10,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'is_active' => true
        ]);

        // Tạo transaction bank data
        $bankData = new BankWebhookData(
            id: 1,
            gateway: 'test_bank',
            transactionDate: now()->toDateTimeString(),
            accountNumber: '*********',
            transactionNumber: 'TXN001',
            content: 'Test deposit',
            transferType: 'IN',
            description: 'Test deposit',
            transferAmount: 100,
            referenceCode: 'REF001'
        );

        // Trigger event
        event(new BankWebhookEvent($this->user->username, $bankData));

        // Refresh user
        $this->user->refresh();

        // Kiểm tra user balance đã được cộng với bonus
        $this->assertEquals(110, $this->user->balance); // 100 + 10% bonus
        $this->assertEquals(110, $this->user->total_deposit);

        // Kiểm tra record trong user_vouchers
        $this->assertDatabaseHas('user_vouchers', [
            'user_id' => $this->user->id,
            'voucher_id' => $voucher->id,
            'tenant_id' => $this->tenant->id,
            'amount_bonus' => 10,
            'original_amount' => 100
        ]);

        // Kiểm tra transaction được tạo
        $this->assertDatabaseHas('transactions', [
            'user_id' => $this->user->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 110,
            'type' => 'deposit'
        ]);
    }

    public function test_bank_webhook_does_not_apply_voucher_second_time()
    {
        // Tạo voucher 10%
        $voucher = Voucher::create([
            'tenant_id' => $this->tenant->id,
            'code' => 'WELCOME10',
            'type' => VoucherTypeEnum::PERCENT,
            'value' => 10,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'is_active' => true
        ]);

        // Lần đầu nạp tiền
        $bankData1 = new BankWebhookData(
            id: 1,
            gateway: 'test_bank',
            transactionDate: now()->toDateTimeString(),
            accountNumber: '*********',
            transactionNumber: 'TXN001',
            content: 'Test deposit 1',
            transferType: 'IN',
            description: 'Test deposit 1',
            transferAmount: 100,
            referenceCode: 'REF001'
        );

        event(new BankWebhookEvent($this->user->username, $bankData1));
        $this->user->refresh();

        // Kiểm tra lần đầu có bonus
        $this->assertEquals(110, $this->user->balance);

        // Lần thứ 2 nạp tiền
        $bankData2 = new BankWebhookData(
            id: 2,
            gateway: 'test_bank',
            transactionDate: now()->toDateTimeString(),
            accountNumber: '*********',
            transactionNumber: 'TXN002',
            content: 'Test deposit 2',
            transferType: 'IN',
            description: 'Test deposit 2',
            transferAmount: 200,
            referenceCode: 'REF002'
        );

        event(new BankWebhookEvent($this->user->username, $bankData2));
        $this->user->refresh();

        // Kiểm tra lần 2 không có bonus (chỉ cộng 200)
        $this->assertEquals(310, $this->user->balance); // 110 + 200 (không có bonus)
        $this->assertEquals(310, $this->user->total_deposit);

        // Kiểm tra chỉ có 1 record trong user_vouchers
        $this->assertEquals(1, $this->user->userVouchers()->count());
    }

    public function test_bank_webhook_without_voucher()
    {
        // Không tạo voucher nào

        $bankData = new BankWebhookData(
            id: 1,
            gateway: 'test_bank',
            transactionDate: now()->toDateTimeString(),
            accountNumber: '*********',
            transactionNumber: 'TXN001',
            content: 'Test deposit',
            transferType: 'IN',
            description: 'Test deposit',
            transferAmount: 100,
            referenceCode: 'REF001'
        );

        event(new BankWebhookEvent($this->user->username, $bankData));
        $this->user->refresh();

        // Kiểm tra chỉ cộng đúng số tiền gốc
        $this->assertEquals(100, $this->user->balance);
        $this->assertEquals(100, $this->user->total_deposit);

        // Kiểm tra không có record trong user_vouchers
        $this->assertEquals(0, $this->user->userVouchers()->count());
    }
}
