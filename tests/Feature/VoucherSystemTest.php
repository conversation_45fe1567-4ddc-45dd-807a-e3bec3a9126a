<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Tenant;
use App\Models\Voucher;
use App\Services\VoucherService;
use App\Enums\VoucherTypeEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VoucherSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $tenant;
    protected $voucherService;

    protected function setUp(): void
    {
        parent::setUp();

        // Tạo user owner
        $owner = User::factory()->create();

        // Tạo tenant
        $this->tenant = Tenant::create([
            'domain' => 'test.example.com',
            'verified' => true,
            'status' => 'published',
            'owner_id' => $owner->id,
            'is_primary' => true
        ]);

        // Tạo user
        $this->user = User::factory()->create();
        $this->user->tenants()->attach($this->tenant->id, ['role' => 'user']);

        $this->voucherService = new VoucherService();
    }

    public function test_user_can_use_voucher_only_once()
    {
        // Tạo voucher percent
        $voucher = Voucher::create([
            'tenant_id' => $this->tenant->id,
            'code' => 'TEST10',
            'type' => VoucherTypeEnum::PERCENT,
            'value' => 10,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'is_active' => true
        ]);

        // Lần đầu sử dụng voucher - phải thành công
        $result1 = $this->voucherService->processDepositWithVoucher($this->user, 100, $this->tenant->id);

        $this->assertTrue($result1['voucher_applied']);
        $this->assertEquals(100, $result1['original_amount']);
        $this->assertEquals(10, $result1['bonus_amount']);
        $this->assertEquals(110, $result1['total_amount']);

        // Kiểm tra record đã được tạo trong user_vouchers
        $this->assertDatabaseHas('user_vouchers', [
            'user_id' => $this->user->id,
            'voucher_id' => $voucher->id,
            'tenant_id' => $this->tenant->id,
            'amount_bonus' => 10,
            'original_amount' => 100
        ]);

        // Lần thứ 2 sử dụng voucher - không được áp dụng
        $result2 = $this->voucherService->processDepositWithVoucher($this->user, 200, $this->tenant->id);

        $this->assertFalse($result2['voucher_applied']);
        $this->assertEquals(200, $result2['original_amount']);
        $this->assertEquals(0, $result2['bonus_amount']);
        $this->assertEquals(200, $result2['total_amount']);
    }

    public function test_fixed_amount_voucher()
    {
        // Tạo voucher fixed amount
        $voucher = Voucher::create([
            'tenant_id' => $this->tenant->id,
            'code' => 'FIXED50',
            'type' => VoucherTypeEnum::FIXED,
            'value' => 50,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'is_active' => true
        ]);

        $result = $this->voucherService->processDepositWithVoucher($this->user, 100, $this->tenant->id);

        $this->assertTrue($result['voucher_applied']);
        $this->assertEquals(100, $result['original_amount']);
        $this->assertEquals(50, $result['bonus_amount']);
        $this->assertEquals(150, $result['total_amount']);
    }

    public function test_expired_voucher_not_applied()
    {
        // Tạo voucher đã hết hạn
        $voucher = Voucher::create([
            'tenant_id' => $this->tenant->id,
            'code' => 'EXPIRED',
            'type' => VoucherTypeEnum::PERCENT,
            'value' => 10,
            'start_date' => now()->subDays(5),
            'end_date' => now()->subDay(),
            'is_active' => true
        ]);

        $result = $this->voucherService->processDepositWithVoucher($this->user, 100, $this->tenant->id);

        $this->assertFalse($result['voucher_applied']);
        $this->assertEquals(100, $result['total_amount']);
    }

    public function test_inactive_voucher_not_applied()
    {
        // Tạo voucher không active
        $voucher = Voucher::create([
            'tenant_id' => $this->tenant->id,
            'code' => 'INACTIVE',
            'type' => VoucherTypeEnum::PERCENT,
            'value' => 10,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'is_active' => false
        ]);

        $result = $this->voucherService->processDepositWithVoucher($this->user, 100, $this->tenant->id);

        $this->assertFalse($result['voucher_applied']);
        $this->assertEquals(100, $result['total_amount']);
    }

    public function test_voucher_model_methods()
    {
        $voucher = Voucher::create([
            'tenant_id' => $this->tenant->id,
            'code' => 'TEST',
            'type' => VoucherTypeEnum::PERCENT,
            'value' => 10,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'is_active' => true
        ]);

        // Test canBeUsedByUser trước khi sử dụng
        $this->assertTrue($voucher->canBeUsedByUser($this->user->id));
        $this->assertFalse($voucher->isUsedByUser($this->user->id));

        // Sử dụng voucher
        $this->voucherService->applyVoucher($this->user, $voucher, 100, $this->tenant->id);

        // Test sau khi sử dụng
        $this->assertFalse($voucher->canBeUsedByUser($this->user->id));
        $this->assertTrue($voucher->isUsedByUser($this->user->id));
    }
}
