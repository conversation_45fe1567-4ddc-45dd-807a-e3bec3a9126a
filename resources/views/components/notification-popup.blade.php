@if($popupPosts && $popupPosts->count() > 0)
    <!-- Notification Popup Modal -->
    <div class="modal fade" id="notificationPopup" tabindex="-1" aria-labelledby="notificationPopupLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        Thông báo mới
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    @foreach($popupPosts as $post)
                        <div class="{{ !$loop->last ? 'border-bottom pb-3 mb-3' : '' }}">
                            <div class="flex-grow-1">
                                <div class="notification-text mb-2">
                                    {!! $post->content !!}
                                </div>
                                @if($post->image)
                                    <div class="notify-image mb-2">
                                        <img src="{{ $post->image }}" alt="{{ $post->title }}" >
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const sessionKey = 'notification_popup_shown_{{ session()->getId() }}';
            const hasShown = sessionStorage.getItem(sessionKey);

            if (!hasShown) {
                setTimeout(function () {
                    const popup = new bootstrap.Modal(document.getElementById('notificationPopup'));
                    popup.show();

                    sessionStorage.setItem(sessionKey, 'true');
                }, 1000);
            }

            document.getElementById('notificationPopup').addEventListener('hidden.bs.modal', function () {
                if (!sessionStorage.getItem(sessionKey)) {
                    sessionStorage.setItem(sessionKey, 'true');
                }
            });
        });
    </script>
    <style>
        .notify-image{
            text-align: center;
            cursor: pointer;
        }
        .notify-image img{
            width: 400px;
            max-width: 100%;
            height: auto;
        }
    </style>
@endif
