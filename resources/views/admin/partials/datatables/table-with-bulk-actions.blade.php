@props([
    'tableId' => 'datatable-ajax',
    'tableClass' => 'table table-bordered datatables',
    'columns' => [],
    'showBulkActions' => true
])

@if($showBulkActions)
    @include('admin.partials.datatables.bulk-actions')
@endif

<div class="table-responsive">
    <table class="{{ $tableClass }}" id="{{ $tableId }}">
        <thead>
            <tr>
                @if($showBulkActions)
                    <th style="width: 50px;">
                        <input type="checkbox" class="form-check-input" id="select-all-checkbox">
                    </th>
                @endif
                @foreach($columns as $column)
                    <th>{{ $column }}</th>
                @endforeach
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
</div>

@if($showBulkActions)
<script>
$(document).ready(function() {
    // Initialize bulk actions when table is ready
    if (typeof bulkActions !== 'undefined') {
        bulkActions.init('#{{ $tableId }}');
    }
});
</script>
@endif
