@extends('clients.layouts.app')
@section('title', 'Bảng điều khiển')
@section('content')
    <div class="row">
        <div class="col-xl-12 col-sm-12">
            <div class="row flex-row-reverse">
                <div class="col-xl-4 col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">
                                Thông báo
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="list-notification">
                                <table class="table-notification">
                                    <tbody>
                                    @forelse($notifications as $item)
                                        <tr class="notify-item">
                                            <td class="notify-item__time">{{ $item->created_at->format('m/Y') }}</td>
                                            <td class="notify-item__content">{!! $item->content !!}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="2">Không có thông báo nào</td>
                                        </tr>
                                    @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">
                                Câu hỏi thường gặp
                            </h4>
                        </div>
                        <div class="card-body">
                            @foreach($faqs as $item)
                                <a
                                    href="#question_{{$item->id}}"
                                    class="question-dash"
                                    data-bs-toggle="collapse"
                                    role="button"
                                    aria-controls="question_{{$item->id}}"
                                    aria-expanded="false">
                                <span class="text-plain">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                         stroke-linejoin="round"
                                         class="icon"><path
                                            stroke="none" d="M0 0h24v24H0z" fill="none"/><path
                                            d="M12 8h8.5a1.5 1.5 0 0 1 0 3h-7.5"/><path
                                            d="M13.5 11h2a1.5 1.5 0 0 1 0 3h-2.5"/><path
                                            d="M14.5 14a1.5 1.5 0 0 1 0 3h-1.5"/><path
                                            d="M13.5 17a1.5 1.5 0 1 1 0 3h-4.5a6 6 0 0 1 -6 -6v-2v.208a6 6 0 0 1 2.7 -5.012l.3 -.196q .718 -.468 5.728 -3.286a1.5 1.5 0 0 1 2.022 .536c.44 .734 .325 1.674 -.28 2.28l-1.47 1.47"/></svg>
                                    {{ $item->question }}
                                </span>
                                    <div class="pt-2 collapse" id="question_{{$item->id}}">
                                        <div class="card-faq-question">
                                            {!! $item->answer !!}
                                        </div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">
                                Hỗ trợ
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @if(tenant_setting('contact_facebook'))
                                    <div class="col-sm-6 mb-3">
                                        <a href="{{ tenant_setting('contact_facebook') }}" target="_blank"
                                           class="widget-support">
                                            <i class="ri-facebook-fill"></i>
                                            <span class="fw-medium">Fanpage Facebook</span>
                                        </a>
                                    </div>
                                @endif
                                @if(tenant_setting('contact_zalo'))
                                    <div class="col-sm-6 mb-3">
                                        <a href="{{ tenant_setting('contact_zalo') }}" target="_blank"
                                           class="widget-support">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                 viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                 stroke-linecap="round" stroke-linejoin="round"
                                                 class="icon text-primary">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M7 4h10l-10 16h10"/>
                                            </svg>
                                            <span class="fw-medium">Zalo</span>
                                        </a>
                                    </div>
                                @endif
                                @if(tenant_setting('contact_telegram'))
                                    <div class="col-sm-6 mb-3">
                                        <a href="{{ tenant_setting('contact_telegram') }}" target="_blank"
                                           class="widget-support">
                                            <i class="ri-telegram-2-line"></i>
                                            <span class="fw-medium">Telegram</span>
                                        </a>
                                    </div>
                                @endif
                                @if(tenant_setting('contact_phone'))
                                    <div class="col-sm-6 mb-3">
                                        <a href="tel:{{ tenant_setting('contact_phone') }}" target="_blank"
                                           class="widget-support">
                                            <i class="ri-phone-line"></i>
                                            <span class="fw-medium">Hotline</span>
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div> @if(tenant_setting('link_youtube'))
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">
                                    Video hướng dẫn
                                </h4>
                            </div>
                            <div class="card-body">
                                <iframe
                                    style="width: 100%;min-height: 350px"
                                    width="560"
                                    height="315"
                                    src="{{ tenant_setting('link_youtube') }}"
                                    title="YouTube video player"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                    referrerpolicy="strict-origin-when-cross-origin"
                                    allowfullscreen></iframe>
                            </div>
                        </div>
                    @endif

                </div>
                <div class="col-xl-8 col-md-6">
                    @include('clients.dashboard.widget-profile')
                    @foreach ($posts as $item)
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <img src="{{ tenant_setting('logo_url') ?? asset('assets/images/user.png') }}"
                                         alt="avatar" class="post-icon align-self-end">
                                    <div>
                                        <a href="{{ tenant_setting('contact_facebook') ?? 'https://www.facebook.com/sniperseeding' }}"
                                           class="post-name">
                                            Quản trị viên
                                            @if ($item->pin)
                                                <i class="ri-pushpin-fill text-danger"></i>
                                            @endif
                                        </a>
                                        <div class="post-time">{{ $item->created_at->format('d-m-Y') }}</div>
                                    </div>
                                </div>
                                <div class="post-content">
                                    {!! $item->content !!}
                                </div>
                                @if(!empty($item->image))
                                    <div class="post-image">
                                        <img src="{{ $item->image }}" alt="image">
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    @include('components.notification-popup', ['popupPosts' => $popupPosts])
@endsection
