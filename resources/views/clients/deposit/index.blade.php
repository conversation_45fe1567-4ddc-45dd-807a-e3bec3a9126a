@extends('clients.layouts.app')
@section('title', 'Nạp tiền')
@section('content')
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-7">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">
                                Thông tin nạp tiền
                            </h4>
                        </div>
                        <div class="card-body">
                            @if(tenant_setting('acb_public'))
                                <table class="table table-deposit">
                                    <tbody>
                                    <tr>
                                        <td>Tên tài khoản</td>
                                        <td>{{ tenant_setting('acb_account_name') }}</td>
                                    </tr>
                                    <tr>
                                        <td>Số tài khoản</td>
                                        <td>
                                            <span class="copy-text" data-copy="{{ tenant_setting('acb_account_number') }}">
                                                {{ tenant_setting('acb_account_number') }}
                                            </span>
                                            <span class="copy-btn" role="button">
                                                <i class="ri-file-copy-line text-success"></i>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Nội dung chuyển khoản</td>
                                        <td>
                                            <span class="copy-text" data-copy="{{ tenant_setting('transfer_content') }}">
                                                {{ tenant_setting('transfer_content') }}
                                            </span>
                                            <span class="copy-btn" role="button">
                                                <i class="ri-file-copy-line text-success"></i>
                                            </span>
                                        </td>
                                    </tr>

                                    </tbody>
                                </table>
                            <div class="text-center">
                                <img
                                    src="https://img.vietqr.io/image/acb-{{tenant_setting('acb_account_number')}}-print.png?accountName={{tenant_setting('acb_account_name')}}&addInfo={{ tenant_setting('transfer_content') . Auth::user()->code }}"
                                    alt="qr-image"
                                    class="qr-code-image"
                                    loading="lazy"
                                />
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-md-5">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">
                                Lưu ý
                                <i class="ri-pushpin-fill text-danger"></i>
                            </h4>
                        </div>
                        <div class="card-body">
                            {{ tenant_setting('note_deposit') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        $(document).on('click', '.copy-btn', function () {
            let copyText = $(this).siblings('.copy-text').data('copy');

            const tempInput = document.createElement("input");
            document.body.appendChild(tempInput);
            tempInput.value = copyText;
            tempInput.select();
            document.execCommand("copy");
            document.body.removeChild(tempInput);
    Base.showSuccess('Sao chép thành công')
        });
    </script>
@endpush
