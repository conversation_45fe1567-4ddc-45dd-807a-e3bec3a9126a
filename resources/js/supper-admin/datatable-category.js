"use strict";

import { definedColumns, xAjax, reloadTable, components, bulkActions } from "../table";
$(document).ready(function() {
  var columns = [
    definedColumns.checkbox,
    definedColumns.stt,
    definedColumns.platform_name,
    definedColumns.name,
    definedColumns.key,
    definedColumns.status,
    definedColumns.created_at,
    definedColumns.action((data, type, row) => {
      return components.btn_edit(row, '/supper-admin/categories') + components.btn_delete(row, '/supper-admin/categories');
    }),
  ];

  reloadTable.datatableLog = $('#datatable-ajax').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/supper-admin/categories/ajax/data`),
    order: [[ 1, "desc" ]], // Changed from 0 to 1 because checkbox is now first column
    columns: columns
  });

  // Initialize bulk actions
  bulkActions.init('#datatable-ajax');
});
