<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_vouchers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('voucher_id')->constrained()->cascadeOnDelete();
            $table->foreignId('tenant_id')->constrained()->cascadeOnDelete();
            $table->decimal('amount_bonus', 15, 2)->default(0);
            $table->decimal('original_amount', 15, 2)->default(0);
            $table->timestamp('used_at');
            $table->timestamps();

            // Đảm bảo một user chỉ có thể sử dụng một voucher một lần
            $table->unique(['user_id', 'voucher_id']);

            // Index để query nhanh
            $table->index(['user_id', 'tenant_id']);
            $table->index(['voucher_id', 'used_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_vouchers');
    }
};
